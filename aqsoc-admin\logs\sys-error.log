2025-08-29 16:10:13.710 [async-task-pool18] ERROR c.a.d.f.s.<PERSON>atFilter - [internalAfterStatementExecute,504] - slow sql 11186 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:09:27"]
2025-08-29 16:12:25.139 [async-task-pool18] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 131403 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:09:27"]
2025-08-29 16:12:28.259 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2414 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***********","2025-08-25 08:27:55","2025-08-29 16:07:15"]
2025-08-29 16:12:28.522 [async-task-pool103] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2773 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["************","2025-08-25 08:12:47","2025-08-29 16:05:25"]
2025-08-29 16:12:28.721 [async-task-pool86] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2968 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 08:02:29","2025-08-29 16:02:46"]
2025-08-29 16:12:29.050 [async-task-pool83] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3303 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**********","2025-08-25 08:14:50","2025-08-29 16:07:15"]
2025-08-29 16:12:42.581 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1153 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:12:27"]
2025-08-29 16:12:43.556 [async-task-pool198] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2129 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:12:34"]
2025-08-29 16:12:44.170 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1586 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:12:27"]
2025-08-29 16:12:46.013 [async-task-pool198] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2454 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:12:34"]
2025-08-29 16:13:01.609 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17436 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:12:27"]
2025-08-29 16:13:16.268 [async-task-pool198] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 30251 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:12:34"]
2025-08-29 16:13:25.497 [async-task-pool51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 44070 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:12:32"]
2025-08-29 16:13:43.881 [async-task-pool51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 18380 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:12:32"]
2025-08-29 16:14:45.542 [async-task-pool51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 61654 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:12:32"]
2025-08-29 16:14:50.287 [async-task-pool188] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2273 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:14:22"]
2025-08-29 16:14:50.690 [async-task-pool180] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1703 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:14:27"]
2025-08-29 16:14:52.721 [async-task-pool188] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2408 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:14:22"]
2025-08-29 16:15:11.513 [async-task-pool180] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20820 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:14:27"]
2025-08-29 16:15:23.367 [async-task-pool188] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 30641 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:14:22"]
2025-08-29 16:15:32.258 [async-task-pool75] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 44243 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:14:23"]
2025-08-29 16:15:48.432 [async-task-pool75] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16170 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:14:23"]
2025-08-29 16:17:45.278 [async-task-pool75] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 116844 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:14:23"]
2025-08-29 16:17:53.337 [async-task-pool56] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7124 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***********","2025-08-25 08:27:55","2025-08-29 16:07:15"]
2025-08-29 16:17:53.580 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7088 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["************","2025-08-25 08:12:47","2025-08-29 16:05:25"]
2025-08-29 16:17:59.940 [async-task-pool132] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5209 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**********","2025-08-25 08:14:50","2025-08-29 16:15:03"]
2025-08-29 16:17:59.945 [async-task-pool165] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5556 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 08:02:29","2025-08-29 16:14:46"]
2025-08-29 16:18:07.137 [async-task-pool184] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3058 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 09:49:03","2025-08-29 16:15:58"]
2025-08-29 16:18:07.259 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3302 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***********","2025-08-26 09:49:05","2025-08-29 16:15:58"]
2025-08-29 16:18:07.299 [async-task-pool52] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3224 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["************","2025-08-25 16:17:50","2025-08-29 16:15:58"]
2025-08-29 16:18:08.390 [async-task-pool168] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4961 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 08:03:15","2025-08-29 16:15:29"]
2025-08-29 16:18:08.512 [async-task-pool38] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4628 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 08:03:53","2025-08-29 16:16:09"]
2025-08-29 16:18:13.506 [async-task-pool67] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1569 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:17:57"]
2025-08-29 16:18:14.608 [async-task-pool67] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1097 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:17:57"]
2025-08-29 16:18:54.910 [async-task-pool67] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 40299 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:17:57"]
2025-08-29 16:19:05.841 [async-task-pool104] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6465 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:18:27"]
2025-08-29 16:19:09.072 [async-task-pool139] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1055 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:18:27"]
2025-08-29 16:19:09.222 [async-task-pool116] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1866 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:18:34"]
2025-08-29 16:19:11.875 [async-task-pool116] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2649 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:18:34"]
2025-08-29 16:19:18.756 [async-task-pool139] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9675 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:18:27"]
2025-08-29 16:19:35.165 [async-task-pool138] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 27808 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:18:35"]
2025-08-29 16:19:36.182 [async-task-pool116] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 24301 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:18:34"]
2025-08-29 16:19:51.753 [async-task-pool138] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16376 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:18:35"]
2025-08-29 16:20:49.549 [async-task-pool138] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 57517 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:18:35"]
2025-08-29 16:20:55.125 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5230 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 08:03:53","2025-08-29 16:16:09"]
2025-08-29 16:21:02.213 [async-task-pool111] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1557 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:20:40"]
2025-08-29 16:21:03.182 [async-task-pool98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1672 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:20:27"]
2025-08-29 16:21:04.279 [async-task-pool111] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2062 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:20:40"]
2025-08-29 16:21:13.217 [async-task-pool98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10031 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:20:27"]
2025-08-29 16:21:28.028 [async-task-pool111] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 23743 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:20:40"]
2025-08-29 16:21:35.552 [async-task-pool143] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9615 millis. select id, risk_assets, risk_type, risk_info, engine_name, start_time, update_time,
               handle_state, handle_desc, disposer, create_time, create_by, update_by, device_config_id
        from ffsafe_flow_risk_assets
     
        where
         (  
            (risk_assets = ?
             AND risk_type = ?
             AND engine_name = ?
             AND start_time = ?)
          OR  
            (risk_assets = ?
             AND risk_type = ?
             AND engine_name = ?
             AND start_time = ?)
          OR  
            (risk_assets = ?
             AND risk_type = ?
             AND engine_name = ?
             AND start_time = ?)
          OR  
            (risk_assets = ?
             AND risk_type = ?
             AND engine_name = ?
             AND start_time = ?)
          OR  
            (risk_assets = ?
             AND risk_type = ?
             AND engine_name = ?
             AND start_time = ?)
         )["**************","weak_password","********","2024-10-31 11:52:33","http://171.34.206.5:82/dp-mng-api/api/oauth/Login","weak_password","********","2025-07-02 10:49:00","http://twx.hyite.cn/h5_rest/v1/loginStep2","sensitive_info","********","2024-10-30 14:22:32","http://xzs.i3210.com/fjjz_rest/v1/memberInfo","sensitive_info","********","2024-10-29 14:41:00","http://twx.hyite.cn/h5_rest/v1/loginStep2","sensitive_info","********","2024-10-30 13:58:43"]
2025-08-29 16:21:35.563 [Thread-6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14289 millis. select id, device_name, device_ip, device_params, create_time, create_by, update_time, update_by, status,filter_log_last_time,alarm_detail_last_time,risk_asset_last_time,host_intrusion_last_time from tbl_device_config
     
         WHERE  `status` = ?[1]
2025-08-29 16:21:35.639 [async-task-pool62] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15918 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",44678,"**************",22,"tcp","[2001219] 疑似 SSH 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",2001219,2,"请求","","[]","2025-08-29 16:21:18",4]
2025-08-29 16:21:35.639 [async-task-pool121] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13092 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-29 16:19:22",1]
2025-08-29 16:21:35.650 [async-task-pool17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13102 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-29 16:19:22",4]
2025-08-29 16:21:35.652 [async-task-pool112] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15837 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",44678,"**************",22,"tcp","[2001219] 疑似 SSH 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",2001219,2,"请求","","[]","2025-08-29 16:21:18",1]
2025-08-29 16:21:35.663 [async-task-pool104] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9699 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",37,"weak_password",5,"sensitive_info",39,"sensitive_info",31,"sensitive_info",1,"['账号:ftpuser, 密码:******']",37,"['账号:og, 密码:******', '账号:13800001114, 密码:******', '账号:nc, 密码:******', '账号:admin, 密码:******', '账号:...",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",39,"['35**************56', '35**************37', '46**************1X', '52**************34', '35*****...",31,"['36**************27', '36**************34', '36**************1X', '36**************24', '36*****...",1,"********",37,"********",5,"********",39,"********",31,"********",1,"2024-10-31 11:52:33",37,"2025-07-02 10:49:00",5,"2024-10-30 14:22:32",39,"2024-10-29 14:41:00",31,"2024-10-30 13:58:43",1,"2025-08-29 16:01:56",37,"2025-08-29 14:43:23",5,"2025-08-29 11:52:09",39,"2025-08-29 11:31:21",31,"2025-08-29 11:29:20",1,1,37,1,5,1,39,1,31,1,1,37,5,39,31]
2025-08-29 16:21:38.587 [async-task-pool94] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 37933 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:20:41"]
2025-08-29 16:21:51.429 [async-task-pool94] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12839 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:20:41"]
2025-08-29 16:22:45.218 [async-task-pool94] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 53785 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:20:41"]
2025-08-29 16:22:55.900 [async-task-pool198] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1120 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:22:45"]
2025-08-29 16:22:57.931 [async-task-pool198] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2027 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:22:45"]
2025-08-29 16:23:06.237 [async-task-pool131] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9752 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:22:27"]
2025-08-29 16:23:17.402 [async-task-pool198] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 19464 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:22:45"]
2025-08-29 16:23:18.624 [async-task-pool63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 23843 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:22:43"]
2025-08-29 16:23:31.290 [async-task-pool63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12660 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:22:43"]
2025-08-29 16:24:17.419 [async-task-pool63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 46126 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:22:43"]
2025-08-29 16:24:19.645 [async-task-pool167] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1097 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:23:27"]
2025-08-29 16:24:19.676 [async-task-pool164] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1795 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:23:48"]
2025-08-29 16:24:22.398 [async-task-pool164] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2718 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:23:48"]
2025-08-29 16:24:34.867 [async-task-pool167] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15218 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:23:27"]
2025-08-29 16:24:44.977 [async-task-pool164] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 22574 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:23:48"]
2025-08-29 16:24:53.059 [async-task-pool51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 35177 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:23:48"]
2025-08-29 16:25:10.619 [async-task-pool51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17557 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:23:48"]
2025-08-29 16:26:14.158 [async-task-pool51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 63533 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:23:48"]
2025-08-29 16:26:34.108 [async-task-pool138] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8590 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:26:15"]
2025-08-29 16:26:42.042 [async-task-pool176] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15693 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:26:09"]
2025-08-29 16:26:43.426 [async-task-pool39] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1065 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:26:09"]
2025-08-29 16:26:45.300 [async-task-pool39] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1867 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:26:09"]
2025-08-29 16:26:53.545 [async-task-pool179] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10261 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:26:15"]
2025-08-29 16:27:03.257 [async-task-pool39] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17953 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:26:09"]
2025-08-29 16:27:10.389 [async-task-pool23] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 28027 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:26:23"]
2025-08-29 16:27:26.602 [async-task-pool23] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16211 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:26:23"]
2025-08-29 16:28:29.873 [async-task-pool23] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 63268 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:26:23"]
2025-08-29 16:28:38.624 [async-task-pool66] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1117 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:28:10"]
2025-08-29 16:28:40.309 [async-task-pool66] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1682 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:28:10"]
2025-08-29 16:28:48.331 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9363 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:28:15"]
2025-08-29 16:28:54.676 [async-task-pool66] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14363 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:28:10"]
2025-08-29 16:28:59.721 [async-task-pool70] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 22214 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:28:23"]
2025-08-29 16:29:11.534 [async-task-pool70] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11810 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:28:23"]
2025-08-29 16:30:09.800 [async-task-pool70] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 58263 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:28:23"]
2025-08-29 16:30:18.479 [async-task-pool195] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1789 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:29:56"]
2025-08-29 16:30:25.324 [async-task-pool87] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8183 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:29:57"]
2025-08-29 16:30:32.224 [async-task-pool195] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13741 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:29:56"]
2025-08-29 16:30:37.110 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 21363 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:29:54"]
2025-08-29 16:30:50.504 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13388 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:29:54"]
2025-08-29 16:31:48.199 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 57692 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:29:54"]
2025-08-29 16:31:52.746 [async-task-pool145] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1061 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:31:27"]
2025-08-29 16:31:53.765 [async-task-pool174] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1975 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:31:29"]
2025-08-29 16:32:02.152 [async-task-pool145] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9402 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:31:27"]
2025-08-29 16:32:08.952 [async-task-pool174] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15182 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:31:29"]
2025-08-29 16:32:14.597 [async-task-pool97] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 23759 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:31:17"]
2025-08-29 16:32:29.204 [async-task-pool97] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14604 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:31:17"]
2025-08-29 16:33:29.375 [async-task-pool97] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 60167 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:31:17"]
2025-08-29 16:33:48.752 [async-task-pool157] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1234 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:33:28"]
2025-08-29 16:33:54.778 [async-task-pool18] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6863 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:33:27"]
2025-08-29 16:33:59.765 [async-task-pool157] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11001 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:33:28"]
2025-08-29 16:34:04.877 [async-task-pool153] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 18073 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:33:20"]
2025-08-29 16:34:14.058 [async-task-pool153] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9177 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:33:20"]
2025-08-29 16:35:11.767 [async-task-pool153] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 57705 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:33:20"]
2025-08-29 16:35:21.614 [async-task-pool130] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1071 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:34:56"]
2025-08-29 16:35:22.864 [async-task-pool130] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1245 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:34:56"]
2025-08-29 16:35:29.067 [async-task-pool98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7584 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:34:57"]
2025-08-29 16:35:36.128 [async-task-pool130] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13259 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:34:56"]
2025-08-29 16:35:41.043 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20501 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:35:03"]
2025-08-29 16:35:53.499 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12453 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:35:03"]
2025-08-29 16:36:59.225 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 65718 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:35:03"]
2025-08-29 16:37:09.284 [async-task-pool124] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6227 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:36:27"]
2025-08-29 16:37:19.257 [async-task-pool22] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17260 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:36:28"]
2025-08-29 16:37:31.392 [async-task-pool22] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12131 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:36:28"]
2025-08-29 16:38:47.295 [async-task-pool22] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 75899 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:36:28"]
2025-08-29 16:38:55.082 [async-task-pool168] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1232 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:38:35"]
2025-08-29 16:38:56.318 [async-task-pool168] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1232 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:38:35"]
2025-08-29 16:39:03.113 [async-task-pool191] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8651 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:38:29"]
2025-08-29 16:39:10.109 [async-task-pool168] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13788 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:38:35"]
2025-08-29 16:39:15.086 [async-task-pool149] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 21236 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:38:30"]
2025-08-29 16:39:26.838 [async-task-pool149] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11749 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:38:30"]
2025-08-29 16:40:22.293 [async-task-pool149] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 55451 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:38:30"]
2025-08-29 16:40:36.380 [async-task-pool42] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6478 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:39:57"]
2025-08-29 16:40:39.168 [async-task-pool34] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1200 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:40:10"]
2025-08-29 16:40:45.089 [async-task-pool29] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7275 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:39:57"]
2025-08-29 16:40:51.963 [async-task-pool34] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12790 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:40:10"]
2025-08-29 16:40:56.959 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 19971 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:40:09"]
2025-08-29 16:41:07.388 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10426 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:40:09"]
2025-08-29 16:41:58.847 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 51453 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:40:09"]
2025-08-29 16:42:11.393 [async-task-pool173] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6162 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:41:27"]
2025-08-29 16:42:15.624 [async-task-pool86] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9871 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:41:41"]
2025-08-29 16:42:19.626 [async-task-pool156] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15228 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:41:38"]
2025-08-29 16:42:31.769 [async-task-pool156] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12140 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:41:38"]
2025-08-29 16:43:25.346 [async-task-pool156] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 53574 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:41:38"]
2025-08-29 16:43:32.209 [async-task-pool77] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1148 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:43:14"]
2025-08-29 16:43:37.184 [async-task-pool15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6146 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:43:15"]
2025-08-29 16:43:43.371 [async-task-pool77] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11158 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:43:14"]
2025-08-29 16:43:47.060 [async-task-pool71] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16906 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:43:14"]
2025-08-29 16:43:57.502 [async-task-pool71] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10439 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:43:14"]
2025-08-29 16:44:05.770 [async-task-pool85] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6866 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-29 16:43:58","/v2/flow-bypass-filtering-log"]
2025-08-29 16:44:05.773 [async-task-pool108] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6877 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-29 16:43:58","/v2/flow-bypass-filtering-log"]
2025-08-29 16:44:05.774 [async-task-pool48] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6358 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",37,"weak_password",5,"sensitive_info",39,"sensitive_info",31,"sensitive_info",1,"['账号:ftpuser, 密码:******']",37,"['账号:og, 密码:******', '账号:13800001114, 密码:******', '账号:nc, 密码:******', '账号:admin, 密码:******', '账号:...",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",39,"['35**************56', '35**************37', '46**************1X', '52**************34', '35*****...",31,"['36**************27', '36**************34', '36**************1X', '36**************24', '36*****...",1,"********",37,"********",5,"********",39,"********",31,"********",1,"2024-10-31 11:52:33",37,"2025-07-02 10:49:00",5,"2024-10-30 14:22:32",39,"2024-10-29 14:41:00",31,"2024-10-30 13:58:43",1,"2025-08-29 16:31:56",37,"2025-08-29 14:43:23",5,"2025-08-29 11:52:09",39,"2025-08-29 11:31:21",31,"2025-08-29 11:29:20",1,1,37,1,5,1,39,1,31,1,1,37,5,39,31]
2025-08-29 16:44:05.775 [async-task-pool106] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6699 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-29 16:41:59",4]
2025-08-29 16:44:05.775 [async-task-pool198] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6355 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",37,"weak_password",5,"sensitive_info",39,"sensitive_info",31,"sensitive_info",1,"['账号:ftpuser, 密码:******']",37,"['账号:og, 密码:******', '账号:13800001114, 密码:******', '账号:nc, 密码:******', '账号:admin, 密码:******', '账号:...",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",39,"['35**************56', '35**************37', '46**************1X', '52**************34', '35*****...",31,"['36**************27', '36**************34', '36**************1X', '36**************24', '36*****...",1,"********",37,"********",5,"********",39,"********",31,"********",1,"2024-10-31 11:52:33",37,"2025-07-02 10:49:00",5,"2024-10-30 14:22:32",39,"2024-10-29 14:41:00",31,"2024-10-30 13:58:43",1,"2025-08-29 16:31:56",37,"2025-08-29 14:43:23",5,"2025-08-29 11:52:09",39,"2025-08-29 11:31:21",31,"2025-08-29 11:29:20",1,4,37,4,5,4,39,4,31,4,1,37,5,39,31]
2025-08-29 16:44:05.777 [async-task-pool107] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6702 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-29 16:41:59",1]
2025-08-29 16:44:05.922 [async-task-pool111] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5187 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",45862,"**************",21,"ftp","[1800403] FTP弱口令登录【成功】","网络攻击/漏洞利用/弱口令","W3siY29udGVudF9oZXgiOiAiMzIzMzMwMjA0YzZmNjc2OTZlMjA3Mzc1NjM2MzY1NzM3MzY2NzU2YyIsICJjb250ZW50X3N0c...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1800403,4,"响应","MjIwICh2c0ZUUGQgMi4yLjIpDQozMzEgUGxlYXNlIHNwZWNpZnkgdGhlIHBhc3N3b3JkLg0KMjMwIExvZ2luIHN1Y2Nlc3Nmd...","[{\"username\":\"ftpuser\"},{\"password\":\"ftpuser\"}]","2025-08-29 16:43:56",1]
2025-08-29 16:44:05.922 [async-task-pool95] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5300 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",45862,"**************",21,"ftp","[1800403] FTP弱口令登录【成功】","网络攻击/漏洞利用/弱口令","W3siY29udGVudF9oZXgiOiAiMzIzMzMwMjA0YzZmNjc2OTZlMjA3Mzc1NjM2MzY1NzM3MzY2NzU2YyIsICJjb250ZW50X3N0c...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1800403,4,"响应","MjIwICh2c0ZUUGQgMi4yLjIpDQozMzEgUGxlYXNlIHNwZWNpZnkgdGhlIHBhc3N3b3JkLg0KMjMwIExvZ2luIHN1Y2Nlc3Nmd...","[{\"username\":\"ftpuser\"},{\"password\":\"ftpuser\"}]","2025-08-29 16:43:56",4]
2025-08-29 16:44:53.364 [async-task-pool71] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 55859 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:43:14"]
2025-08-29 16:45:04.488 [async-task-pool180] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5086 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:44:27"]
2025-08-29 16:45:12.522 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13812 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:44:33"]
2025-08-29 16:45:19.957 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7424 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:44:33"]
2025-08-29 16:46:05.031 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 45070 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:44:33"]
2025-08-29 16:46:14.153 [async-task-pool143] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4067 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:45:27"]
2025-08-29 16:46:20.431 [async-task-pool142] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4200 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:45:27"]
2025-08-29 16:46:31.426 [async-task-pool139] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9288 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:46:15"]
2025-08-29 16:46:35.229 [async-task-pool126] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12566 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:46:09"]
2025-08-29 16:46:38.706 [async-task-pool6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1220 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:46:09"]
2025-08-29 16:46:46.681 [async-task-pool1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8981 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:46:15"]
2025-08-29 16:46:53.909 [async-task-pool6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15195 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:46:09"]
2025-08-29 16:46:58.700 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 21891 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:46:20"]
2025-08-29 16:47:09.378 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10674 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:46:20"]
2025-08-29 16:47:49.066 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 39685 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:46:20"]
2025-08-29 16:47:55.332 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4701 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:47:27"]
2025-08-29 16:47:57.913 [async-task-pool41] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1217 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:47:35"]
2025-08-29 16:47:59.166 [async-task-pool41] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1250 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:47:35"]
2025-08-29 16:48:06.103 [async-task-pool128] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8380 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:47:27"]
2025-08-29 16:48:12.364 [async-task-pool41] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13194 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:47:35"]
2025-08-29 16:48:15.512 [async-task-pool115] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 18816 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:47:40"]
2025-08-29 16:48:25.729 [async-task-pool115] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10213 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:47:40"]
2025-08-29 16:49:00.739 [async-task-pool115] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 35004 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:47:40"]
2025-08-29 16:49:02.333 [async-task-pool126] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1244 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:48:37"]
2025-08-29 16:49:04.059 [async-task-pool126] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1724 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:48:37"]
2025-08-29 16:49:10.595 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7870 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:48:27"]
2025-08-29 16:49:14.907 [async-task-pool126] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10845 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:48:37"]
2025-08-29 16:49:18.344 [async-task-pool177] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17259 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:48:52"]
2025-08-29 16:49:26.677 [async-task-pool177] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8330 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:48:52"]
2025-08-29 16:50:00.809 [async-task-pool177] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 34129 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:48:52"]
2025-08-29 16:50:08.729 [async-task-pool158] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5500 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:49:27"]
2025-08-29 16:50:11.648 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1122 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:49:39"]
2025-08-29 16:50:13.840 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2188 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:49:39"]
2025-08-29 16:50:28.343 [async-task-pool44] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16413 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:49:27"]
2025-08-29 16:50:35.160 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 21317 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:49:39"]
2025-08-29 16:50:36.947 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 26422 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:49:47"]
2025-08-29 16:50:46.066 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9116 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:49:47"]
2025-08-29 16:52:24.404 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 98334 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:49:47"]
2025-08-29 16:52:25.820 [async-task-pool52] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1045 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**********","2025-08-25 08:14:50","2025-08-29 16:45:03"]
2025-08-29 16:52:26.066 [async-task-pool137] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1291 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["***********","2025-08-25 08:27:55","2025-08-29 16:43:15"]
2025-08-29 16:52:26.127 [async-task-pool29] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1351 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["************","2025-08-25 16:17:50","2025-08-29 16:42:31"]
2025-08-29 16:52:27.216 [async-task-pool137] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1107 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***********","2025-08-25 08:27:55","2025-08-29 16:43:15"]
2025-08-29 16:52:27.705 [async-task-pool29] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1562 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["************","2025-08-25 16:17:50","2025-08-29 16:42:31"]
2025-08-29 16:52:28.001 [async-task-pool52] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2029 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**********","2025-08-25 08:14:50","2025-08-29 16:45:03"]
2025-08-29 16:52:29.391 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3912 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["************","2025-08-25 08:12:47","2025-08-29 16:45:42"]
2025-08-29 16:52:36.759 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1642 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:52:22"]
2025-08-29 16:52:37.356 [async-task-pool15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1287 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:52:15"]
2025-08-29 16:52:39.140 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2377 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:52:22"]
2025-08-29 16:52:46.686 [async-task-pool15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9323 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:52:15"]
2025-08-29 16:53:01.788 [async-task-pool92] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 26671 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:52:22"]
2025-08-29 16:53:15.336 [async-task-pool92] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13545 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:52:22"]
2025-08-29 16:53:30.584 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 51434 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:52:22"]
2025-08-29 16:53:55.506 [async-task-pool92] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 40165 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:52:22"]
2025-08-29 16:54:15.087 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16125 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:53:45"]
2025-08-29 16:54:25.181 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10088 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:53:45"]
2025-08-29 16:55:04.088 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 38903 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:53:45"]
2025-08-29 16:55:06.064 [async-task-pool55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1687 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["************","2025-08-25 16:17:50","2025-08-29 16:54:06"]
2025-08-29 16:55:16.162 [async-task-pool172] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1521 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:54:57"]
2025-08-29 16:55:18.040 [async-task-pool172] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1875 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:54:57"]
2025-08-29 16:55:27.106 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10829 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:54:57"]
2025-08-29 16:55:32.489 [async-task-pool172] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14445 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:54:57"]
2025-08-29 16:55:36.690 [async-task-pool146] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 22050 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:54:58"]
2025-08-29 16:55:49.985 [async-task-pool146] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13291 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:54:58"]
2025-08-29 16:56:39.557 [async-task-pool146] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 49568 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:54:58"]
2025-08-29 16:56:43.782 [async-task-pool158] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1386 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:56:18"]
2025-08-29 16:56:45.736 [async-task-pool158] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1950 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:56:18"]
2025-08-29 16:56:54.123 [async-task-pool199] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10323 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:56:27"]
2025-08-29 16:56:59.546 [async-task-pool158] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13807 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:56:18"]
2025-08-29 16:57:03.734 [async-task-pool84] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 21338 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:56:21"]
2025-08-29 16:57:14.684 [async-task-pool84] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10936 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:56:21"]
2025-08-29 16:58:10.304 [async-task-pool84] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 55404 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:56:21"]
2025-08-29 16:58:18.667 [async-task-pool176] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1708 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:57:57"]
2025-08-29 16:58:19.136 [async-task-pool168] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1223 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:57:57"]
2025-08-29 16:58:20.545 [async-task-pool176] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1876 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:57:57"]
2025-08-29 16:58:30.855 [async-task-pool168] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11712 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:57:57"]
2025-08-29 16:58:37.518 [async-task-pool176] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16968 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:57:57"]
2025-08-29 16:58:42.015 [async-task-pool57] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 25058 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:58:02"]
2025-08-29 16:58:54.091 [async-task-pool57] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12071 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:58:02"]
2025-08-29 16:59:40.934 [async-task-pool57] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 46840 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:58:02"]
2025-08-29 16:59:53.107 [async-task-pool79] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1462 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 16:59:27"]
2025-08-29 16:59:54.616 [async-task-pool79] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1505 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 16:59:27"]
2025-08-29 17:00:03.811 [async-task-pool179] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10330 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:59:27"]
2025-08-29 17:00:10.682 [async-task-pool79] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16061 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 16:59:27"]
2025-08-29 17:00:14.405 [async-task-pool100] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 22759 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 16:59:18"]
2025-08-29 17:00:23.392 [async-task-pool100] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8982 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 16:59:18"]
2025-08-29 17:01:00.064 [async-task-pool100] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 36667 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 16:59:18"]
2025-08-29 17:01:18.658 [async-task-pool60] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8005 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:00:57"]
2025-08-29 17:01:22.703 [async-task-pool96] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1352 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:01:10"]
2025-08-29 17:01:23.054 [async-task-pool127] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1022 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:00:57"]
2025-08-29 17:01:24.907 [async-task-pool96] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2201 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:01:10"]
2025-08-29 17:01:32.062 [async-task-pool127] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9002 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:00:57"]
2025-08-29 17:01:40.481 [async-task-pool96] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15568 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:01:10"]
2025-08-29 17:01:42.271 [async-task-pool128] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20921 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:01:03"]
2025-08-29 17:01:53.074 [async-task-pool128] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10798 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:01:03"]
2025-08-29 17:02:02.025 [async-task-pool145] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1281 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",37,"weak_password",5,"sensitive_info",39,"sensitive_info",31,"sensitive_info",1,"['账号:ftpuser, 密码:******']",37,"['账号:og, 密码:******', '账号:13800001114, 密码:******', '账号:nc, 密码:******', '账号:admin, 密码:******', '账号:...",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",39,"['35**************56', '35**************37', '46**************1X', '52**************34', '35*****...",31,"['36**************27', '36**************34', '36**************1X', '36**************24', '36*****...",1,"********",37,"********",5,"********",39,"********",31,"********",1,"2024-10-31 11:52:33",37,"2025-07-02 10:49:00",5,"2024-10-30 14:22:32",39,"2024-10-29 14:41:00",31,"2024-10-30 13:58:43",1,"2025-08-29 16:31:56",37,"2025-08-29 14:43:23",5,"2025-08-29 11:52:09",39,"2025-08-29 11:31:21",31,"2025-08-29 11:29:20",1,1,37,1,5,1,39,1,31,1,1,37,5,39,31]
2025-08-29 17:02:02.026 [async-task-pool173] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1248 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",37,"weak_password",5,"sensitive_info",39,"sensitive_info",31,"sensitive_info",1,"['账号:ftpuser, 密码:******']",37,"['账号:og, 密码:******', '账号:13800001114, 密码:******', '账号:nc, 密码:******', '账号:admin, 密码:******', '账号:...",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",39,"['35**************56', '35**************37', '46**************1X', '52**************34', '35*****...",31,"['36**************27', '36**************34', '36**************1X', '36**************24', '36*****...",1,"********",37,"********",5,"********",39,"********",31,"********",1,"2024-10-31 11:52:33",37,"2025-07-02 10:49:00",5,"2024-10-30 14:22:32",39,"2024-10-29 14:41:00",31,"2024-10-30 13:58:43",1,"2025-08-29 16:31:56",37,"2025-08-29 14:43:23",5,"2025-08-29 11:52:09",39,"2025-08-29 11:31:21",31,"2025-08-29 11:29:20",1,4,37,4,5,4,39,4,31,4,1,37,5,39,31]
2025-08-29 17:02:34.830 [async-task-pool128] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 41753 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:01:03"]
2025-08-29 17:02:37.595 [async-task-pool25] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1369 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:02:10"]
2025-08-29 17:02:39.157 [async-task-pool25] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1558 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:02:10"]
2025-08-29 17:02:44.596 [async-task-pool36] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7169 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:01:57"]
2025-08-29 17:02:51.067 [async-task-pool25] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11906 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:02:10"]
2025-08-29 17:02:54.147 [async-task-pool8] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17920 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:01:54"]
2025-08-29 17:03:06.481 [async-task-pool8] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12327 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:01:54"]
2025-08-29 17:03:58.002 [async-task-pool8] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 51518 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:01:54"]
2025-08-29 17:04:03.215 [async-task-pool57] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1357 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:03:42"]
2025-08-29 17:04:10.335 [async-task-pool100] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8188 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:03:27"]
2025-08-29 17:04:15.433 [async-task-pool57] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12215 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:03:42"]
2025-08-29 17:04:18.782 [async-task-pool96] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17847 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:03:37"]
2025-08-29 17:04:28.307 [async-task-pool96] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9520 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:03:37"]
2025-08-29 17:05:18.138 [async-task-pool96] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 49825 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:03:37"]
2025-08-29 17:05:29.373 [async-task-pool112] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1084 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:05:16"]
2025-08-29 17:05:30.616 [async-task-pool112] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1240 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:05:16"]
2025-08-29 17:05:38.725 [async-task-pool93] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9667 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:05:15"]
2025-08-29 17:05:44.019 [async-task-pool112] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13398 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:05:16"]
2025-08-29 17:05:47.026 [async-task-pool102] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 18738 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:05:05"]
2025-08-29 17:05:56.143 [async-task-pool102] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9114 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:05:05"]
2025-08-29 17:06:28.810 [async-task-pool102] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 32664 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:05:05"]
2025-08-29 17:06:35.189 [async-task-pool115] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2386 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-29 17:04:32",4]
2025-08-29 17:06:35.209 [async-task-pool63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2389 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-29 17:04:32",1]
2025-08-29 17:06:35.211 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1876 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-29 17:06:33","/v2/flow-bypass-filtering-log"]
2025-08-29 17:06:35.211 [async-task-pool183] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1869 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-29 17:06:33","/v2/flow-bypass-filtering-log"]
2025-08-29 17:06:35.221 [pool-6-thread-3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6068 millis. UPDATE tbl_attack_alarm SET
        risk_level = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE risk_level
        END,
        location = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE location
        END,
        victim_ip_nums = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE victim_ip_nums
        END,
        attack_type_nums = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE attack_type_nums
        END,
        attack_nums = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE attack_nums
        END,
        update_time = CASE id
          
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
           
            WHEN ? THEN  ?   
         
        ELSE update_time
        END[1175,3,1174,2,1176,3,1181,1,1182,1,1183,1,1179,1,1206,3,1224,1,1194,2,1175,"局域网",1174,"局域网",1176,"局域网",1181,"局域网",1182,"局域网",1183,"局域网",1179,"局域网",1206,"局域网",1224,"局域网",1194,"局域网",1175,9,1174,2,1176,2,1181,2,1182,2,1183,2,1179,1,1206,7,1224,1,1194,4,1175,9,1174,5,1176,2,1181,1,1182,1,1183,1,1179,1,1206,7,1224,3,1194,5,1175,188641,1174,94361,1176,2335369,1181,4271,1182,4257,1183,3947,1179,4584,1206,2214,1224,1591,1194,1432,1175,"2025-08-29 17:05:16",1174,"2025-08-29 17:05:15",1176,"2025-08-29 17:05:05",1181,"2025-08-29 17:04:09",1182,"2025-08-29 17:03:30",1183,"2025-08-29 17:02:47",1179,"2025-08-29 17:00:03",1206,"2025-08-29 16:58:07",1224,"2025-08-29 16:57:58",1194,"2025-08-29 16:57:58"]
2025-08-29 17:06:35.241 [async-task-pool150] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1635 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",37,"weak_password",5,"sensitive_info",39,"sensitive_info",31,"sensitive_info",1,"['账号:ftpuser, 密码:******']",37,"['账号:og, 密码:******', '账号:13800001114, 密码:******', '账号:nc, 密码:******', '账号:admin, 密码:******', '账号:...",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",39,"['35**************56', '35**************37', '46**************1X', '52**************34', '35*****...",31,"['36**************27', '36**************34', '36**************1X', '36**************24', '36*****...",1,"********",37,"********",5,"********",39,"********",31,"********",1,"2024-10-31 11:52:33",37,"2025-07-02 10:49:00",5,"2024-10-30 14:22:32",39,"2024-10-29 14:41:00",31,"2024-10-30 13:58:43",1,"2025-08-29 17:01:56",37,"2025-08-29 14:43:23",5,"2025-08-29 11:52:09",39,"2025-08-29 11:31:21",31,"2025-08-29 11:29:20",1,4,37,4,5,4,39,4,31,4,1,37,5,39,31]
2025-08-29 17:06:35.292 [async-task-pool157] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9515 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",11796,"**************",3306,"tcp","[1300005] 疑似 MySQL 3306 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300005,2,"请求","","[]","2025-08-29 17:06:19",4]
2025-08-29 17:06:35.292 [async-task-pool170] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9401 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            
            
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            
            
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",11796,"**************",3306,"tcp","[1300005] 疑似 MySQL 3306 端口扫描（端口访问速率较高）","网络攻击/网络扫描探测/其他侦查","W10=","af1eda48-6135-11ef-8e67-000c29677ec8","********",1300005,2,"请求","","[]","2025-08-29 17:06:19",1]
2025-08-29 17:06:35.293 [async-task-pool165] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1676 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",37,"weak_password",5,"sensitive_info",39,"sensitive_info",31,"sensitive_info",1,"['账号:ftpuser, 密码:******']",37,"['账号:og, 密码:******', '账号:13800001114, 密码:******', '账号:nc, 密码:******', '账号:admin, 密码:******', '账号:...",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",39,"['35**************56', '35**************37', '46**************1X', '52**************34', '35*****...",31,"['36**************27', '36**************34', '36**************1X', '36**************24', '36*****...",1,"********",37,"********",5,"********",39,"********",31,"********",1,"2024-10-31 11:52:33",37,"2025-07-02 10:49:00",5,"2024-10-30 14:22:32",39,"2024-10-29 14:41:00",31,"2024-10-30 13:58:43",1,"2025-08-29 17:01:56",37,"2025-08-29 14:43:23",5,"2025-08-29 11:52:09",39,"2025-08-29 11:31:21",31,"2025-08-29 11:29:20",1,1,37,1,5,1,39,1,31,1,1,37,5,39,31]
2025-08-29 17:06:36.481 [async-task-pool187] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1085 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:05:57"]
2025-08-29 17:06:38.144 [async-task-pool187] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1656 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:05:57"]
2025-08-29 17:06:45.582 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9125 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:05:57"]
2025-08-29 17:06:52.422 [async-task-pool187] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14269 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:05:57"]
2025-08-29 17:06:54.745 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 19348 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:05:57"]
2025-08-29 17:07:06.096 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11344 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:05:57"]
2025-08-29 17:07:43.574 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 37472 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:05:57"]
2025-08-29 17:07:47.872 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1256 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:07:16"]
2025-08-29 17:07:53.843 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7418 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:07:18"]
2025-08-29 17:07:58.288 [async-task-pool19] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10412 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:07:16"]
2025-08-29 17:08:02.050 [async-task-pool54] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16356 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:07:03"]
2025-08-29 17:08:11.167 [async-task-pool54] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9112 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:07:03"]
2025-08-29 17:08:49.269 [async-task-pool54] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 38099 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:07:03"]
2025-08-29 17:08:52.629 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1270 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:08:50"]
2025-08-29 17:08:53.714 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1081 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:08:50"]
2025-08-29 17:09:01.912 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9193 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:08:28"]
2025-08-29 17:09:07.721 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14003 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:08:50"]
2025-08-29 17:09:11.853 [async-task-pool18] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20494 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:08:47"]
2025-08-29 17:09:29.296 [async-task-pool18] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17439 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:08:47"]
2025-08-29 17:11:34.895 [async-task-pool18] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 125596 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:08:47"]
2025-08-29 17:11:36.377 [async-task-pool86] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1018 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["*************","2025-08-25 08:03:15","2025-08-29 17:03:30"]
2025-08-29 17:11:36.417 [async-task-pool142] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1059 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["*************","2025-08-25 08:02:29","2025-08-29 17:02:47"]
2025-08-29 17:11:36.467 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1110 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["*************","2025-08-25 08:03:53","2025-08-29 17:04:09"]
2025-08-29 17:11:36.614 [async-task-pool70] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1237 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***********","2025-08-25 14:42:32","2025-08-29 17:06:25"]
2025-08-29 17:11:36.914 [async-task-pool120] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1224 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***********","2025-08-26 09:49:05","2025-08-29 16:57:58"]
2025-08-29 17:11:41.562 [async-task-pool86] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5120 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 08:03:15","2025-08-29 17:03:30"]
2025-08-29 17:11:42.128 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5580 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 08:03:53","2025-08-29 17:04:09"]
2025-08-29 17:11:42.325 [async-task-pool142] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 5787 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 08:02:29","2025-08-29 17:02:47"]
2025-08-29 17:11:43.007 [async-task-pool148] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6521 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**********","2025-08-25 08:14:50","2025-08-29 17:00:03"]
2025-08-29 17:11:44.513 [async-task-pool178] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1048 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["************","2025-08-25 08:12:47","2025-08-29 17:08:27"]
2025-08-29 17:11:46.078 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2684 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:11:47.302 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1221 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:11:48.009 [async-task-pool82] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3183 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:09:06"]
2025-08-29 17:11:49.095 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1789 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:11:51.847 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2273 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:11:53.082 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1232 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:11:53.313 [async-task-pool63] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2860 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:09:06"]
2025-08-29 17:11:54.312 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1225 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:11:57.422 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1671 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:11:58.033 [async-task-pool42] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1830 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:09:06"]
2025-08-29 17:11:59.771 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1496 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:03.537 [async-task-pool87] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1361 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:04.745 [async-task-pool87] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1173 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:04.832 [async-task-pool68] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1821 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:09:06"]
2025-08-29 17:12:07.324 [async-task-pool65] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1293 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:07.687 [async-task-pool73] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1208 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:09:06"]
2025-08-29 17:12:08.511 [async-task-pool65] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1184 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:10.380 [async-task-pool65] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1864 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:12.517 [async-task-pool138] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1241 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["*************","2025-08-25 08:03:53","2025-08-29 17:04:09"]
2025-08-29 17:12:12.960 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1763 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:14.214 [async-task-pool64] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2393 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:09:06"]
2025-08-29 17:12:14.325 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1360 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:15.559 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1231 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:18.209 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2086 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:18.799 [async-task-pool158] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1919 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:09:06"]
2025-08-29 17:12:19.332 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1118 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:20.664 [async-task-pool171] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1328 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:22.961 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1632 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:23.415 [async-task-pool44] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1478 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:09:06"]
2025-08-29 17:12:25.262 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1420 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:26.878 [async-task-pool111] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1190 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:27.395 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1083 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:09:06"]
2025-08-29 17:12:28.791 [async-task-pool111] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1179 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:30.242 [async-task-pool163] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1127 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:31.080 [async-task-pool93] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1304 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:09:06"]
2025-08-29 17:12:31.245 [async-task-pool163] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1000 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:33.880 [async-task-pool130] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1318 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:34.365 [async-task-pool112] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1306 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:09:06"]
2025-08-29 17:12:37.090 [async-task-pool136] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1016 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:40.283 [async-task-pool142] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1220 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:40.568 [async-task-pool173] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1047 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:09:06"]
2025-08-29 17:12:42.210 [async-task-pool142] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1125 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:43.565 [async-task-pool115] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1040 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:44.037 [async-task-pool56] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1062 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:09:06"]
2025-08-29 17:12:45.479 [async-task-pool115] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1272 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:47.248 [async-task-pool164] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1224 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:47.642 [async-task-pool61] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1097 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:09:06"]
2025-08-29 17:12:50.480 [async-task-pool6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1178 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:51.050 [async-task-pool133] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1149 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:09:06"]
2025-08-29 17:12:53.575 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1154 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:12:13"]
2025-08-29 17:12:53.938 [async-task-pool42] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1515 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:54.515 [async-task-pool187] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1399 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:09:06"]
2025-08-29 17:12:55.247 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1662 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:12:13"]
2025-08-29 17:12:55.364 [async-task-pool42] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1422 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:12:56.628 [async-task-pool42] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1262 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:09:05"]
2025-08-29 17:13:02.842 [async-task-pool184] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9364 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:12:15"]
2025-08-29 17:13:09.777 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14525 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:12:13"]
2025-08-29 17:13:14.283 [async-task-pool1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 21861 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:12:14"]
2025-08-29 17:13:25.591 [async-task-pool1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11305 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:12:14"]
2025-08-29 17:14:19.686 [async-task-pool1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 54092 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:12:14"]
2025-08-29 17:14:23.795 [async-task-pool140] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1186 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:13:58"]
2025-08-29 17:14:24.289 [async-task-pool110] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1679 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:13:04"]
2025-08-29 17:14:25.715 [async-task-pool140] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1916 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:13:58"]
2025-08-29 17:14:25.790 [async-task-pool93] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2390 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:14:25.832 [async-task-pool110] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1539 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:13:04"]
2025-08-29 17:14:27.244 [async-task-pool110] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1366 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:13:04"]
2025-08-29 17:14:33.565 [async-task-pool104] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9436 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:13:57"]
2025-08-29 17:14:39.843 [async-task-pool140] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 14122 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:13:58"]
2025-08-29 17:14:43.476 [async-task-pool196] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20867 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:13:51"]
2025-08-29 17:14:53.154 [async-task-pool196] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9674 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:13:51"]
2025-08-29 17:15:43.457 [async-task-pool196] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 50297 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:13:51"]
2025-08-29 17:15:45.344 [async-task-pool96] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1430 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:13:04"]
2025-08-29 17:15:46.120 [async-task-pool38] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1553 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:15:47.534 [async-task-pool96] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1246 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:13:04"]
2025-08-29 17:15:49.076 [async-task-pool25] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1213 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:13:04"]
2025-08-29 17:15:49.795 [async-task-pool156] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1336 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:15:50.982 [async-task-pool25] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1087 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:13:04"]
2025-08-29 17:15:52.388 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1141 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:13:04"]
2025-08-29 17:15:54.547 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1214 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:13:04"]
2025-08-29 17:15:55.839 [async-task-pool154] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1014 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:15:10"]
2025-08-29 17:15:56.115 [async-task-pool87] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1290 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:13:04"]
2025-08-29 17:15:57.081 [async-task-pool68] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1609 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:15:57.163 [async-task-pool87] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1043 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:13:04"]
2025-08-29 17:15:57.448 [async-task-pool154] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1607 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:15:10"]
2025-08-29 17:15:58.799 [async-task-pool87] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1632 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:13:04"]
2025-08-29 17:16:12.988 [async-task-pool154] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15534 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:15:10"]
2025-08-29 17:16:16.498 [async-task-pool3] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2429 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:16:18.669 [async-task-pool158] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1065 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:15:57"]
2025-08-29 17:16:19.491 [async-task-pool15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1888 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:16:00"]
2025-08-29 17:16:19.789 [async-task-pool158] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1112 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:15:57"]
2025-08-29 17:16:19.852 [async-task-pool107] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2247 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:15:44"]
2025-08-29 17:16:20.925 [async-task-pool114] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2220 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:16:21.645 [async-task-pool15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2150 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:16:00"]
2025-08-29 17:16:21.713 [async-task-pool107] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1858 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:15:44"]
2025-08-29 17:16:24.133 [async-task-pool107] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2416 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:15:44"]
2025-08-29 17:16:33.606 [async-task-pool158] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13814 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:15:57"]
2025-08-29 17:16:41.824 [async-task-pool15] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20176 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:16:00"]
2025-08-29 17:16:47.003 [async-task-pool88] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 29401 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:15:57"]
2025-08-29 17:17:00.298 [async-task-pool88] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13293 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:15:57"]
2025-08-29 17:17:42.352 [async-task-pool88] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 42050 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:15:57"]
2025-08-29 17:17:44.278 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1482 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:15:44"]
2025-08-29 17:17:45.327 [async-task-pool164] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1721 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:17:47.721 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1349 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:15:44"]
2025-08-29 17:17:48.780 [async-task-pool181] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1609 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:17:52.068 [async-task-pool28] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1495 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:17:53.959 [async-task-pool34] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1517 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:17:31"]
2025-08-29 17:17:54.321 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1120 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:17:27"]
2025-08-29 17:17:54.397 [async-task-pool96] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1953 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:15:44"]
2025-08-29 17:17:56.066 [async-task-pool96] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1666 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:15:44"]
2025-08-29 17:17:56.183 [async-task-pool34] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2221 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:17:31"]
2025-08-29 17:17:56.520 [async-task-pool184] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3316 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:17:57.647 [async-task-pool96] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1577 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:15:44"]
2025-08-29 17:18:04.482 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10157 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:17:27"]
2025-08-29 17:18:11.346 [async-task-pool34] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15160 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:17:31"]
2025-08-29 17:18:17.106 [async-task-pool40] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 24663 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:17:32"]
2025-08-29 17:18:37.506 [async-task-pool40] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20396 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:17:32"]
2025-08-29 17:19:30.539 [async-task-pool40] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 53029 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:17:32"]
2025-08-29 17:19:32.586 [async-task-pool141] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1258 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:19:34.660 [async-task-pool54] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1252 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:19:36.280 [async-task-pool145] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1026 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:19:37.886 [async-task-pool100] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1091 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:19:40.436 [async-task-pool132] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1592 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:19:47.435 [async-task-pool173] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1405 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:18:01"]
2025-08-29 17:19:47.934 [async-task-pool83] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1304 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:19:49.256 [async-task-pool173] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1064 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:18:01"]
2025-08-29 17:19:50.749 [async-task-pool178] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1116 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:18:01"]
2025-08-29 17:19:51.507 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1417 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:19:52.607 [async-task-pool178] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1084 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:18:01"]
2025-08-29 17:19:56.412 [async-task-pool165] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1252 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:19:58.522 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1411 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:18:01"]
2025-08-29 17:19:59.323 [async-task-pool21] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1469 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:20:01.927 [async-task-pool6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1176 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**********","2025-08-25 08:14:50","2025-08-29 17:19:16"]
2025-08-29 17:20:02.022 [async-task-pool27] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1123 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:59:12","2025-08-29 17:19:33"]
2025-08-29 17:20:02.039 [async-task-pool0] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1471 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:19:27"]
2025-08-29 17:20:02.489 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1918 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:19:10"]
2025-08-29 17:20:03.150 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2579 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:18:01"]
2025-08-29 17:20:04.397 [async-task-pool180] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2392 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:20:04.434 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1942 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:19:10"]
2025-08-29 17:20:04.506 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1353 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:18:01"]
2025-08-29 17:20:06.068 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1558 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:18:01"]
2025-08-29 17:20:12.504 [async-task-pool0] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9629 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:19:27"]
2025-08-29 17:20:16.528 [async-task-pool109] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12090 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:19:10"]
2025-08-29 17:20:24.573 [async-task-pool35] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 24004 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:19:23"]
2025-08-29 17:20:40.961 [async-task-pool35] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16385 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:19:23"]
2025-08-29 17:21:43.531 [async-task-pool35] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 62566 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:19:23"]
2025-08-29 17:21:45.327 [async-task-pool122] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1005 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:21:48.433 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1236 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:21:49.296 [async-task-pool110] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1555 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:21:53.438 [async-task-pool145] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1057 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:21:54.253 [async-task-pool84] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1326 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:21:58.554 [async-task-pool170] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1137 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:02.166 [async-task-pool174] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1103 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:03.255 [async-task-pool162] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1038 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:05.113 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1592 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:06.217 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2074 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:06.259 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1141 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:07.759 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1496 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:09.777 [async-task-pool11] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1687 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:10.587 [async-task-pool36] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1726 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:10.844 [async-task-pool11] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1064 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:12.146 [async-task-pool11] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1297 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:13.978 [async-task-pool153] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1523 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:14.796 [async-task-pool30] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1586 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:17.061 [async-task-pool156] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1412 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:17.841 [async-task-pool193] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1507 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:19.354 [async-task-pool156] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1371 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:21.143 [async-task-pool32] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1310 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:22.081 [async-task-pool68] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1545 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:24.572 [async-task-pool194] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1303 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:25.179 [async-task-pool177] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1178 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:27.903 [async-task-pool176] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1321 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:28.324 [async-task-pool4] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1177 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:30.060 [async-task-pool176] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1252 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:31.743 [async-task-pool138] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1306 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:32.428 [async-task-pool10] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1439 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:35.114 [async-task-pool67] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1546 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:35.780 [async-task-pool77] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1431 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:38.383 [async-task-pool114] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1349 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:39.463 [async-task-pool17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1612 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:39.471 [async-task-pool114] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1084 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:40.715 [async-task-pool114] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1240 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:42.175 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1194 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:42.723 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1183 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:43.324 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1144 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:44.449 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1121 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:45.862 [async-task-pool111] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1086 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:46.829 [async-task-pool22] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1569 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:47.085 [async-task-pool111] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1218 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:48.300 [async-task-pool111] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1213 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:50.189 [async-task-pool110] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1415 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:50.793 [async-task-pool130] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1555 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:52.155 [async-task-pool110] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1166 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:53.931 [async-task-pool104] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1323 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:54.226 [async-task-pool84] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1163 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:56.096 [async-task-pool104] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1319 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:57.755 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1235 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:22:58.428 [async-task-pool148] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1293 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:22:58.859 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1098 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:23:00.033 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1170 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:23:01.818 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1362 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:23:02.342 [async-task-pool88] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1418 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:23:04.136 [async-task-pool147] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1313 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:23:05.838 [async-task-pool115] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1313 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:23:06.760 [async-task-pool94] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1728 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:23:07.049 [async-task-pool115] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1209 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:23:08.495 [async-task-pool115] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1443 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:19:56"]
2025-08-29 17:23:11.871 [async-task-pool52] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1149 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:23:16.533 [async-task-pool153] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1218 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:23:09"]
2025-08-29 17:23:17.293 [async-task-pool161] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1975 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:23:17.940 [async-task-pool76] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1862 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:23:18.575 [async-task-pool153] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2039 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:23:09"]
2025-08-29 17:23:18.877 [async-task-pool161] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1580 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:23:21.066 [async-task-pool161] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2185 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:23:28.010 [async-task-pool25] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11281 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:22:57"]
2025-08-29 17:23:38.379 [async-task-pool153] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 19800 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:23:09"]
2025-08-29 17:23:46.406 [async-task-pool33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 31088 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:23:02"]
2025-08-29 17:23:59.268 [async-task-pool33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12847 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:23:02"]
2025-08-29 17:24:42.737 [async-task-pool33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 43464 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:23:02"]
2025-08-29 17:24:44.909 [async-task-pool70] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1146 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:24:47.699 [async-task-pool160] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1211 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:24:48.283 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1138 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:24:49.760 [async-task-pool160] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1197 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:24:51.871 [async-task-pool140] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1155 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:24:56.314 [async-task-pool57] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1037 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:24:59.409 [async-task-pool178] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1071 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:25:00.616 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1013 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:25:01.715 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1095 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:25:01.723 [async-task-pool164] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1569 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:25:03.000 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1261 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:25:04.796 [async-task-pool174] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1574 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:25:06.042 [async-task-pool174] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1233 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:25:06.592 [async-task-pool21] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2667 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["***************","2025-08-25 11:30:48","2025-08-29 17:13:39"]
2025-08-29 17:25:07.705 [async-task-pool174] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1658 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:25:09.677 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1682 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:24:39"]
2025-08-29 17:25:09.688 [async-task-pool180] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1690 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:25:09.860 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1103 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:24:27"]
2025-08-29 17:25:10.840 [async-task-pool180] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1143 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:25:11.599 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1904 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:24:39"]
2025-08-29 17:25:12.592 [async-task-pool180] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1592 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:25:20.083 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10112 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:24:27"]
2025-08-29 17:25:27.535 [async-task-pool26] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15931 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:24:39"]
2025-08-29 17:25:33.499 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 25503 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:24:44"]
2025-08-29 17:25:50.077 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16548 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:24:44"]
2025-08-29 17:26:47.510 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 57397 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:24:44"]
2025-08-29 17:26:50.439 [async-task-pool149] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1001 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:26:51.842 [async-task-pool158] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1139 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:26:56.315 [async-task-pool121] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1035 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:26:58.798 [async-task-pool99] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1015 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:27:02.800 [async-task-pool100] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1048 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:27:13.812 [async-task-pool56] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1002 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:27:24.071 [async-task-pool187] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1155 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:27:26.916 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1186 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:27:29.700 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1944 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:27:31.732 [async-task-pool184] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1430 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:27:33.743 [async-task-pool184] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1272 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:27:35.701 [async-task-pool13] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1342 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:27:37.458 [async-task-pool13] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1148 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:27:39.260 [async-task-pool87] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1198 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:27:41.173 [async-task-pool87] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1198 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:27:42.842 [async-task-pool195] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1371 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:27:45.591 [async-task-pool195] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1960 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:27:47.465 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1384 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:27:49.766 [async-task-pool182] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1346 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:27:52.439 [async-task-pool176] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1032 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:28:04.271 [async-task-pool143] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1009 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:28:12.427 [async-task-pool141] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1165 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:27:30"]
2025-08-29 17:28:20.390 [async-task-pool120] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7911 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:27:27"]
2025-08-29 17:28:24.416 [async-task-pool141] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11061 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:27:30"]
2025-08-29 17:29:06.881 [async-task-pool192] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10858 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-29 17:28:55","/v2/flow-bypass-filtering-log"]
2025-08-29 17:29:06.882 [async-task-pool164] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9970 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",37,"weak_password",5,"sensitive_info",39,"sensitive_info",31,"sensitive_info",1,"['账号:ftpuser, 密码:******']",37,"['账号:og, 密码:******', '账号:13800001114, 密码:******', '账号:nc, 密码:******', '账号:admin, 密码:******', '账号:...",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",39,"['35**************56', '35**************37', '46**************1X', '52**************34', '35*****...",31,"['36**************27', '36**************34', '36**************1X', '36**************24', '36*****...",1,"********",37,"********",5,"********",39,"********",31,"********",1,"2024-10-31 11:52:33",37,"2025-07-02 10:49:00",5,"2024-10-30 14:22:32",39,"2024-10-29 14:41:00",31,"2024-10-30 13:58:43",1,"2025-08-29 17:01:56",37,"2025-08-29 14:43:23",5,"2025-08-29 11:52:09",39,"2025-08-29 11:31:21",31,"2025-08-29 11:29:20",1,1,37,1,5,1,39,1,31,1,1,37,5,39,31]
2025-08-29 17:29:06.886 [async-task-pool167] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10866 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-29 17:26:55",4]
2025-08-29 17:29:06.886 [async-task-pool7] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10825 millis. update tbl_device_config
         SET host_intrusion_last_time = ? 
        where id = ?["2025-08-29 17:26:55",1]
2025-08-29 17:29:06.887 [async-task-pool61] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10889 millis. update ffsafe_interface_config
         SET data_last_time = ? 
        where
        interface_path = ?["2025-08-29 17:28:55","/v2/flow-bypass-filtering-log"]
2025-08-29 17:29:06.888 [async-task-pool21] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 9959 millis. update ffsafe_flow_risk_assets
             set risk_type =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                risk_info =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                engine_name =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                start_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                update_time =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end,
                device_config_id =case when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ?
                    
                        when id=? then ? end 
            where id in
             (  
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             , 
                ?
             )[1,"weak_password",37,"weak_password",5,"sensitive_info",39,"sensitive_info",31,"sensitive_info",1,"['账号:ftpuser, 密码:******']",37,"['账号:og, 密码:******', '账号:13800001114, 密码:******', '账号:nc, 密码:******', '账号:admin, 密码:******', '账号:...",5,"['36**************27', '36**************34', '36**************1X', '36**************33', '36*****...",39,"['35**************56', '35**************37', '46**************1X', '52**************34', '35*****...",31,"['36**************27', '36**************34', '36**************1X', '36**************24', '36*****...",1,"********",37,"********",5,"********",39,"********",31,"********",1,"2024-10-31 11:52:33",37,"2025-07-02 10:49:00",5,"2024-10-30 14:22:32",39,"2024-10-29 14:41:00",31,"2024-10-30 13:58:43",1,"2025-08-29 17:01:56",37,"2025-08-29 14:43:23",5,"2025-08-29 11:52:09",39,"2025-08-29 11:31:21",31,"2025-08-29 11:29:20",1,4,37,4,5,4,39,4,31,4,1,37,5,39,31]
2025-08-29 17:29:09.331 [async-task-pool148] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15300 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",57313,"**************",80,"http","[1780003] web路径遍历漏洞攻击-Linux环境","异常行为/访问异常","W3sicGNyZSI6ICJcXC9ldGNcXC9wYXNzd2R8JTVjZXRjXFwvcGFzc3dkfDB4NWNldGNcXC9wYXNzd2R8JTJmZXRjXFwvcGFzc...",404,"SFRUUC8xLjEgNDA0CkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8IURPQ1RZUEUgaHRtbD4KP...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1780003,4,"请求","R0VUIC93ZWJ1aS8/Zz1zeXNfZGlhX2RhdGFfZG93biZmaWxlX25hbWU9Li4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vL...","[]","2025-08-29 17:28:39",4]
2025-08-29 17:29:09.410 [async-task-pool139] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15371 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",57313,"**************",80,"http","[1780003] web路径遍历漏洞攻击-Linux环境","异常行为/访问异常","W3sicGNyZSI6ICJcXC9ldGNcXC9wYXNzd2R8JTVjZXRjXFwvcGFzc3dkfDB4NWNldGNcXC9wYXNzd2R8JTJmZXRjXFwvcGFzc...",404,"SFRUUC8xLjEgNDA0CkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8IURPQ1RZUEUgaHRtbD4KP...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1780003,4,"请求","R0VUIC93ZWJ1aS8/Zz1zeXNfZGlhX2RhdGFfZG93biZmaWxlX25hbWU9Li4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vL...","[]","2025-08-29 17:28:39",1]
2025-08-29 17:29:16.098 [async-task-pool139] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1382 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",56728,"**************",80,"http","[1701884] TOTOLINK EX1200T ExportSettings.sh 信息泄露漏洞(CVE-2021-42886)攻击","其他/web后门","W3siY29udGVudF9oZXgiOiAiNDc0NTU0IiwgImNvbnRlbnRfc3RyaW5nIjogIkdFVCJ9LCB7ImNvbnRlbnRfaGV4IjogIjJmN...",404,"SFRUUC8xLjEgNDA0CkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8IURPQ1RZUEUgaHRtbD4KP...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1701884,4,"请求","R0VUIC9jZ2ktYmluL0V4cG9ydFNldHRpbmdzLnNoIEhUVFAvMS4xDQpIb3N0OiB3d3cuZ3N0YXRpYy5jb20NClVzZXItQWdlb...","[]","2025-08-29 17:28:36",1]
2025-08-29 17:29:16.149 [async-task-pool148] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1388 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",56728,"**************",80,"http","[1701884] TOTOLINK EX1200T ExportSettings.sh 信息泄露漏洞(CVE-2021-42886)攻击","其他/web后门","W3siY29udGVudF9oZXgiOiAiNDc0NTU0IiwgImNvbnRlbnRfc3RyaW5nIjogIkdFVCJ9LCB7ImNvbnRlbnRfaGV4IjogIjJmN...",404,"SFRUUC8xLjEgNDA0CkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCgo8IURPQ1RZUEUgaHRtbD4KP...","af1eda48-6135-11ef-8e67-000c29677ec8","********",1701884,4,"请求","R0VUIC9jZ2ktYmluL0V4cG9ydFNldHRpbmdzLnNoIEhUVFAvMS4xDQpIb3N0OiB3d3cuZ3N0YXRpYy5jb20NClVzZXItQWdlb...","[]","2025-08-29 17:28:36",4]
2025-08-29 17:29:18.340 [async-task-pool148] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1019 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",56395,"**************",80,"http","[1802145] H3C企业级路由器(ERG2系列、NR系列、GR系列)用户登录远程命令执行漏洞漏洞攻击","网络攻击/漏洞利用/恶意代码攻击","W3siY29udGVudF9oZXgiOiAiNDc0NTU0IiwgImNvbnRlbnRfc3RyaW5nIjogIkdFVCJ9LCB7ImNvbnRlbnRfaGV4IjogIjJmN...",302,"SFRUUC8xLjEgMzAyCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCg==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1802145,4,"请求","R0VUIC9saXZlX21mZy5zaHRtbCBIVFRQLzEuMQ0KSG9zdDogd3d3LmdzdGF0aWMuY29tDQpVc2VyLUFnZW50OiBNb3ppbGxhL...","[]","2025-08-29 17:28:35",4]
2025-08-29 17:29:18.411 [async-task-pool139] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1115 millis. insert into ffsafe_flow_detail
         ( sip,
            sport,
            dip,
            dport,
            procotol,
            threaten_name,
            threaten_type,
            feature_en,
            
            response_code,
            response_en,
            
            probe_id,
            probe_ip,
            rule_id,
            alarm_level,
            payload_title,
            payload_en,
            
            meta_data,
            create_time,
            
            device_config_id ) 
         values ( ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ?,
            ?,
            ?,
            ?,
            ?,
            ?,
            
            ?,
            ?,
            
            ? )["**************",56395,"**************",80,"http","[1802145] H3C企业级路由器(ERG2系列、NR系列、GR系列)用户登录远程命令执行漏洞漏洞攻击","网络攻击/漏洞利用/恶意代码攻击","W3siY29udGVudF9oZXgiOiAiNDc0NTU0IiwgImNvbnRlbnRfc3RyaW5nIjogIkdFVCJ9LCB7ImNvbnRlbnRfaGV4IjogIjJmN...",302,"SFRUUC8xLjEgMzAyCkNvbnRlbnQtTGVuZ3RoOiAwCkNvbnRlbnQtVHlwZTogdGV4dC9odG1sCg==","af1eda48-6135-11ef-8e67-000c29677ec8","********",1802145,4,"请求","R0VUIC9saXZlX21mZy5zaHRtbCBIVFRQLzEuMQ0KSG9zdDogd3d3LmdzdGF0aWMuY29tDQpVc2VyLUFnZW50OiBNb3ppbGxhL...","[]","2025-08-29 17:28:35",1]
2025-08-29 17:29:26.031 [async-task-pool110] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 74763 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:29:28.068 [async-task-pool110] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1282 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:23:02"]
2025-08-29 17:29:42.380 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1286 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:29:43.763 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1364 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:29:45.596 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1830 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:29:47.399 [async-task-pool33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1513 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:29:48.436 [async-task-pool33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1003 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:29:49.708 [async-task-pool33] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1260 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:29:52.159 [async-task-pool93] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1366 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:29:53.177 [async-task-pool93] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1000 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:29:54.492 [async-task-pool93] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1311 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:29:58.718 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1060 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:30:00.495 [async-task-pool175] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1001 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:30:03.601 [async-task-pool82] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 4972 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:29:27"]
2025-08-29 17:30:10.374 [async-task-pool164] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3699 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:29:27"]
2025-08-29 17:30:14.807 [async-task-pool188] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3363 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:29:27"]
2025-08-29 17:30:20.369 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1093 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:29:47"]
2025-08-29 17:30:21.091 [async-task-pool185] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1022 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:30:26.846 [async-task-pool191] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7005 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:29:27"]
2025-08-29 17:30:31.383 [async-task-pool135] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10990 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:29:47"]
2025-08-29 17:30:33.581 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15136 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:29:47"]
2025-08-29 17:30:47.422 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 13836 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:29:47"]
2025-08-29 17:31:35.698 [async-task-pool159] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 48273 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:29:47"]
2025-08-29 17:31:38.527 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1903 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:31:39.721 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1192 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:31:41.763 [async-task-pool2] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 2036 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:31:43.324 [async-task-pool45] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1145 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:31:45.498 [async-task-pool45] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1197 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:31:46.806 [async-task-pool163] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1089 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:31:49.376 [async-task-pool163] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1890 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:31:51.188 [async-task-pool112] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1433 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:31:56.857 [async-task-pool100] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1261 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:31:59.607 [async-task-pool169] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1070 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:32:01.589 [async-task-pool169] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1278 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:32:04.533 [async-task-pool118] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1006 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:32:14.419 [async-task-pool6] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1413 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:32:15.968 [async-task-pool52] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1229 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:31:50"]
2025-08-29 17:32:16.478 [async-task-pool146] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1738 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:32:17.705 [async-task-pool52] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1732 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:31:50"]
2025-08-29 17:32:17.807 [async-task-pool146] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1326 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:32:19.566 [async-task-pool146] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1747 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:32:27.454 [async-task-pool110] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11302 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:31:56"]
2025-08-29 17:32:34.428 [async-task-pool52] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16716 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:31:50"]
2025-08-29 17:32:38.138 [async-task-pool28] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 23399 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:31:55"]
2025-08-29 17:32:46.919 [async-task-pool28] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8777 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:31:55"]
2025-08-29 17:33:31.956 [async-task-pool28] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 45033 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:31:55"]
2025-08-29 17:33:33.839 [async-task-pool98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1232 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:33:36.413 [async-task-pool98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1580 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:33:38.031 [async-task-pool107] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1168 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:33:40.118 [async-task-pool107] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1384 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:33:41.694 [async-task-pool35] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1231 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:33:43.556 [async-task-pool35] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1133 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:33:44.986 [async-task-pool70] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1014 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:33:46.010 [async-task-pool70] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1021 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:33:47.365 [async-task-pool70] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1352 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:33:51.452 [async-task-pool84] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1029 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:33:52.856 [async-task-pool20] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1463 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:33:09"]
2025-08-29 17:33:53.790 [async-task-pool84] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1354 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:33:59.290 [async-task-pool160] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7800 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:33:27"]
2025-08-29 17:34:05.175 [async-task-pool20] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12297 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:33:09"]
2025-08-29 17:34:09.782 [async-task-pool140] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 19362 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:33:16"]
2025-08-29 17:34:20.008 [async-task-pool140] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10222 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:33:16"]
2025-08-29 17:35:13.184 [async-task-pool140] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 53173 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:33:16"]
2025-08-29 17:35:15.293 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1483 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:35:17.739 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1464 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:35:19.306 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1062 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:35:20.864 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1081 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:35:22.638 [async-task-pool39] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1239 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:35:28.685 [async-task-pool125] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1187 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:35:39.952 [async-task-pool152] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1388 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:35:40.816 [async-task-pool163] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1303 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:35:16"]
2025-08-29 17:35:42.025 [async-task-pool152] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1282 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:35:47.016 [async-task-pool35] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7246 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:35:27"]
2025-08-29 17:35:52.656 [async-task-pool163] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 11831 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:35:16"]
2025-08-29 17:35:59.368 [async-task-pool81] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 20806 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:35:28"]
2025-08-29 17:36:11.771 [async-task-pool81] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12400 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:35:28"]
2025-08-29 17:36:50.553 [async-task-pool81] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 38778 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:35:28"]
2025-08-29 17:36:52.183 [async-task-pool38] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1028 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:36:59.846 [async-task-pool43] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1159 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:37:01.879 [async-task-pool43] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1265 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:37:06.394 [async-task-pool51] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1301 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:37:16.833 [async-task-pool98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1032 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:37:18.622 [async-task-pool98] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1213 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:37:28.063 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12262 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:36:42"]
2025-08-29 17:37:36.318 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8251 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:36:42"]
2025-08-29 17:38:22.005 [async-task-pool50] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 45682 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:36:42"]
2025-08-29 17:38:27.279 [async-task-pool94] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1021 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:38:28.756 [async-task-pool81] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1090 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:38:30.711 [async-task-pool81] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1066 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:38:32.137 [async-task-pool146] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1125 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:38:52.605 [async-task-pool91] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1239 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:38:55.783 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1126 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:38:56.808 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1014 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:38:57.014 [async-task-pool53] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1415 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:38:23"]
2025-08-29 17:38:58.640 [async-task-pool14] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1829 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:39:09.457 [async-task-pool53] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 12439 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:38:23"]
2025-08-29 17:39:12.479 [async-task-pool17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 17826 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:38:20"]
2025-08-29 17:39:18.992 [async-task-pool17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6506 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:38:20"]
2025-08-29 17:39:48.134 [async-task-pool17] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 29139 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:38:20"]
2025-08-29 17:39:51.640 [async-task-pool178] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1219 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:39:55.747 [async-task-pool88] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1029 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:40:01.752 [async-task-pool24] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1058 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 14:04:10","2025-08-29 17:28:49"]
2025-08-29 17:40:11.916 [async-task-pool58] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 3232 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:39:57"]
2025-08-29 17:40:16.884 [async-task-pool85] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1148 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:02","2025-08-29 17:40:04"]
2025-08-29 17:40:18.179 [async-task-pool85] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1253 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:40:04"]
2025-08-29 17:40:24.494 [async-task-pool1] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7570 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:39:57"]
2025-08-29 17:40:28.876 [async-task-pool85] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 10689 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:40:04"]
2025-08-29 17:40:31.634 [async-task-pool199] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15897 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:40:04"]
2025-08-29 17:40:39.039 [async-task-pool199] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7402 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:40:04"]
2025-08-29 17:41:16.041 [async-task-pool199] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 36998 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:40:04"]
2025-08-29 17:41:29.175 [async-task-pool110] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1296 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:40:59"]
2025-08-29 17:41:36.436 [async-task-pool96] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8425 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:40:57"]
2025-08-29 17:41:44.454 [async-task-pool110] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15276 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:40:59"]
2025-08-29 17:41:49.115 [async-task-pool55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 22164 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:41:01"]
2025-08-29 17:41:56.322 [async-task-pool55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 7205 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:41:01"]
2025-08-29 17:42:27.489 [async-task-pool55] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 31159 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:41:01"]
2025-08-29 17:42:43.948 [async-task-pool112] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6590 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:41:57"]
2025-08-29 17:42:51.050 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15027 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:41:53"]
2025-08-29 17:42:59.718 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8664 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:41:53"]
2025-08-29 17:43:33.985 [async-task-pool49] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 34264 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:41:53"]
2025-08-29 17:43:49.659 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 1094 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:02","2025-08-29 17:43:36"]
2025-08-29 17:43:56.974 [async-task-pool75] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8458 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:43:27"]
2025-08-29 17:44:04.981 [async-task-pool189] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 15317 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:43:36"]
2025-08-29 17:44:09.968 [async-task-pool143] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 22284 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:43:38"]
2025-08-29 17:44:18.837 [async-task-pool143] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8854 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:43:38"]
2025-08-29 17:44:51.313 [async-task-pool143] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 32473 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:43:38"]
2025-08-29 17:45:10.494 [async-task-pool113] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 6633 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:02","2025-08-29 17:44:27"]
2025-08-29 17:45:18.914 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 16123 millis. SELECT
        count(*)
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ?["**************","2025-08-25 08:00:04","2025-08-29 17:44:30"]
2025-08-29 17:45:27.521 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 8604 millis. SELECT
        dip
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time >= ?
             and create_time <= ? 
        GROUP BY dip["**************","2025-08-25 08:00:04","2025-08-29 17:44:30"]
2025-08-29 17:46:03.608 [async-task-pool12] ERROR c.a.d.f.s.StatFilter - [internalAfterStatementExecute,504] - slow sql 36081 millis. SELECT
        sip,
        threaten_name as threatenName,
        MAX(alarm_level) AS alarmLevel,
        MAX(threaten_type) AS threatenType,
        COUNT(*) AS count
        FROM ffsafe_flow_detail
         WHERE  sip = ?
            
            
             and create_time between ? and ? 
        GROUP BY sip, threaten_name["**************","2025-08-25 08:00:04","2025-08-29 17:44:30"]
2025-08-29 17:46:21.153 [pool-6-thread-4] ERROR c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,517] - 发送攻击告警同步消息失败: attackIp=**********, 错误: Error creating bean with name 'redisCache': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'redisCache': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:233)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1282)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1243)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:494)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:342)
	at cn.hutool.extra.spring.SpringUtil.getBean(SpringUtil.java:122)
	at com.ruoyi.rabbitmq.domain.SyncMessage.setData(SyncMessage.java:72)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl.sendDataSyncMessageAsync(TblAttackAlarmServiceImpl.java:510)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl.sync(TblAttackAlarmServiceImpl.java:293)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl$$FastClassBySpringCGLIB$$e90ea721.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl$$EnhancerBySpringCGLIB$$5d40da2a.sync(<generated>)
	at com.ruoyi.safe.task.FfSafeFlowAlarmTask.syncTask(FfSafeFlowAlarmTask.java:96)
	at com.ruoyi.ffsafe.api.service.impl.ApiResultSeviceImpl.lambda$5(ApiResultSeviceImpl.java:202)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-29 17:46:21.159 [pool-6-thread-4] ERROR c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,517] - 发送攻击告警同步消息失败: attackIp=***********, 错误: Error creating bean with name 'redisCache': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'redisCache': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:233)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1282)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1243)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:494)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:342)
	at cn.hutool.extra.spring.SpringUtil.getBean(SpringUtil.java:122)
	at com.ruoyi.rabbitmq.domain.SyncMessage.setData(SyncMessage.java:72)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl.sendDataSyncMessageAsync(TblAttackAlarmServiceImpl.java:510)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl.sync(TblAttackAlarmServiceImpl.java:293)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl$$FastClassBySpringCGLIB$$e90ea721.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl$$EnhancerBySpringCGLIB$$5d40da2a.sync(<generated>)
	at com.ruoyi.safe.task.FfSafeFlowAlarmTask.syncTask(FfSafeFlowAlarmTask.java:96)
	at com.ruoyi.ffsafe.api.service.impl.ApiResultSeviceImpl.lambda$5(ApiResultSeviceImpl.java:202)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-29 17:46:21.176 [pool-6-thread-4] ERROR c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,517] - 发送攻击告警同步消息失败: attackIp=*************, 错误: Error creating bean with name 'redisCache': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'redisCache': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:233)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1282)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1243)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:494)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:342)
	at cn.hutool.extra.spring.SpringUtil.getBean(SpringUtil.java:122)
	at com.ruoyi.rabbitmq.domain.SyncMessage.setData(SyncMessage.java:72)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl.sendDataSyncMessageAsync(TblAttackAlarmServiceImpl.java:510)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl.sync(TblAttackAlarmServiceImpl.java:293)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl$$FastClassBySpringCGLIB$$e90ea721.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl$$EnhancerBySpringCGLIB$$5d40da2a.sync(<generated>)
	at com.ruoyi.safe.task.FfSafeFlowAlarmTask.syncTask(FfSafeFlowAlarmTask.java:96)
	at com.ruoyi.ffsafe.api.service.impl.ApiResultSeviceImpl.lambda$5(ApiResultSeviceImpl.java:202)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-29 17:46:21.187 [pool-6-thread-4] ERROR c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,517] - 发送攻击告警同步消息失败: attackIp=*************, 错误: Error creating bean with name 'redisCache': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'redisCache': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:233)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1282)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1243)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:494)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:342)
	at cn.hutool.extra.spring.SpringUtil.getBean(SpringUtil.java:122)
	at com.ruoyi.rabbitmq.domain.SyncMessage.setData(SyncMessage.java:72)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl.sendDataSyncMessageAsync(TblAttackAlarmServiceImpl.java:510)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl.sync(TblAttackAlarmServiceImpl.java:293)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl$$FastClassBySpringCGLIB$$e90ea721.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl$$EnhancerBySpringCGLIB$$5d40da2a.sync(<generated>)
	at com.ruoyi.safe.task.FfSafeFlowAlarmTask.syncTask(FfSafeFlowAlarmTask.java:96)
	at com.ruoyi.ffsafe.api.service.impl.ApiResultSeviceImpl.lambda$5(ApiResultSeviceImpl.java:202)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-29 17:46:21.205 [pool-6-thread-4] ERROR c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,517] - 发送攻击告警同步消息失败: attackIp=*************, 错误: Error creating bean with name 'redisCache': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'redisCache': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:233)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1282)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1243)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:494)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:342)
	at cn.hutool.extra.spring.SpringUtil.getBean(SpringUtil.java:122)
	at com.ruoyi.rabbitmq.domain.SyncMessage.setData(SyncMessage.java:72)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl.sendDataSyncMessageAsync(TblAttackAlarmServiceImpl.java:510)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl.sync(TblAttackAlarmServiceImpl.java:293)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl$$FastClassBySpringCGLIB$$e90ea721.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl$$EnhancerBySpringCGLIB$$5d40da2a.sync(<generated>)
	at com.ruoyi.safe.task.FfSafeFlowAlarmTask.syncTask(FfSafeFlowAlarmTask.java:96)
	at com.ruoyi.ffsafe.api.service.impl.ApiResultSeviceImpl.lambda$5(ApiResultSeviceImpl.java:202)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-29 17:46:21.229 [pool-6-thread-4] ERROR c.r.t.s.i.TblAttackAlarmServiceImpl - [sendDataSyncMessageAsync,517] - 发送攻击告警同步消息失败: attackIp=*************, 错误: Error creating bean with name 'redisCache': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'redisCache': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:220)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:233)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1282)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveNamedBean(DefaultListableBeanFactory.java:1243)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveBean(DefaultListableBeanFactory.java:494)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:349)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.getBean(DefaultListableBeanFactory.java:342)
	at cn.hutool.extra.spring.SpringUtil.getBean(SpringUtil.java:122)
	at com.ruoyi.rabbitmq.domain.SyncMessage.setData(SyncMessage.java:72)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl.sendDataSyncMessageAsync(TblAttackAlarmServiceImpl.java:510)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl.sync(TblAttackAlarmServiceImpl.java:293)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl$$FastClassBySpringCGLIB$$e90ea721.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.ruoyi.threaten.service.impl.TblAttackAlarmServiceImpl$$EnhancerBySpringCGLIB$$5d40da2a.sync(<generated>)
	at com.ruoyi.safe.task.FfSafeFlowAlarmTask.syncTask(FfSafeFlowAlarmTask.java:96)
	at com.ruoyi.ffsafe.api.service.impl.ApiResultSeviceImpl.lambda$5(ApiResultSeviceImpl.java:202)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
