2025-08-29 16:43:04.378 [Thread-7] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 128毫秒
2025-08-29 16:43:06.818 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-29 16:43:06.818 [taskScheduler-18] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-29 16:43:06.820 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-29 16:43:06.820 [taskScheduler-18] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：1,等待任务: 0
2025-08-29 16:43:06.821 [async-task-pool11] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$null$0,90] - 开始获取IP阻断日志: 测试非凡设备1
2025-08-29 16:43:06.821 [async-task-pool137] INFO  c.r.f.a.e.PullFilterLogEvent - [lambda$null$0,90] - 开始获取IP阻断日志: 测试设备2
2025-08-29 16:43:06.835 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,128] - 开始执行任务,任务数:2...
2025-08-29 16:43:06.835 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,130] - 当前活跃线程：5,等待任务: 0
2025-08-29 16:43:06.835 [async-task-pool29] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$null$0,83] - 开始获取主机入侵攻击数据: 测试设备2
2025-08-29 16:43:06.835 [async-task-pool35] INFO  c.r.f.a.e.PullHostIntrusionAttackEvent - [lambda$null$0,83] - 开始获取主机入侵攻击数据: 测试非凡设备1
2025-08-29 16:43:06.835 [async-task-pool29] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-28 16:41:03, endDate=2025-08-29 16:41:06, page=1, pSize=20, deviceConfigId=4)
2025-08-29 16:43:06.836 [async-task-pool35] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,874] - 开始拉取主机入侵攻击数据，参数：HostIntrusionAttackParam(accessToken=null, sip=null, dip=null, startDate=2025-08-28 16:41:03, endDate=2025-08-29 16:41:06, page=1, pSize=20, deviceConfigId=1)
2025-08-29 16:43:06.837 [async-task-pool29] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-28%2016%3A41%3A03&end_date=2025-08-29%2016%3A41%3A06&page=1&p_size=20
2025-08-29 16:43:06.837 [async-task-pool35] INFO  c.r.f.a.d.HostIntrusionAttackParam - [getRequestBase,80] - 非凡API主机入侵攻击分页请求参数: https://***************:23000/v2/host-edr-intrusion?access_token=lst2BAzxJTQL0eSZ&start_date=2025-08-28%2016%3A41%3A03&end_date=2025-08-29%2016%3A41%3A06&page=1&p_size=20
2025-08-29 16:43:06.893 [async-task-pool24] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: {"message":"无数据"}

2025-08-29 16:43:06.897 [async-task-pool52] INFO  c.r.f.c.FFSafeRequestComponent - [execRequest,88] - 非凡请求响应: {"message":"无数据"}

2025-08-29 16:43:06.901 [Thread-6] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 83毫秒
2025-08-29 16:43:06.905 [async-task-pool29] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-29 16:43:06.906 [async-task-pool29] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1007] - 第1页获取0条数据
2025-08-29 16:43:06.906 [taskScheduler-18] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 88毫秒
2025-08-29 16:43:06.906 [async-task-pool29] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1011] - 当前页无数据，停止获取
2025-08-29 16:43:06.907 [async-task-pool29] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1080] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-29 16:43:06.911 [async-task-pool35] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,987] - 主机入侵攻击总数: 0
2025-08-29 16:43:06.911 [async-task-pool35] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1007] - 第1页获取0条数据
2025-08-29 16:43:06.912 [async-task-pool35] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1011] - 当前页无数据，停止获取
2025-08-29 16:43:06.912 [async-task-pool35] INFO  c.r.m.c.c.FfsafeClientService - [pullHostIntrusionAttacks,1080] - 拉取主机入侵攻击数据成功，但没有有效数据
2025-08-29 16:43:06.917 [Thread-8] INFO  c.r.c.utils.Threads - [batchAsyncExecute,135] - 任务执行完成,耗时: 82毫秒
